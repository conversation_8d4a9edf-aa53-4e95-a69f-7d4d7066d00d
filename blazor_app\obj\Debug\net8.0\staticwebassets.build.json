{"Version": 1, "Hash": "ED6mIR1m5gTh1YY3VVQFr8L+wJSK9OB6k/QjKrEDhnI=", "Source": "EmployeeRatingSystem.Blazor", "BasePath": "_content/EmployeeRatingSystem.Blazor", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "EmployeeRatingSystem.Blazor\\wwwroot", "Source": "EmployeeRatingSystem.Blazor", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "SourceId": "Microsoft.AspNetCore.Components.QuickGrid", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\", "BasePath": "_content/Microsoft.AspNetCore.Components.QuickGrid", "RelativePath": "Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "25o87uqmvr", "Integrity": "Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "FileLength": 8251, "LastWriteTime": "2025-07-08T16:22:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\QuickGrid.razor.js", "SourceId": "Microsoft.AspNetCore.Components.QuickGrid", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\", "BasePath": "_content/Microsoft.AspNetCore.Components.QuickGrid", "RelativePath": "QuickGrid.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eu7w3d5g1a", "Integrity": "tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\QuickGrid.razor.js", "FileLength": 2563, "LastWriteTime": "2025-07-08T14:22:06+00:00"}, {"Identity": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\bundle\\EmployeeRatingSystem.Blazor.styles.css", "SourceId": "EmployeeRatingSystem.Blazor", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "RelativePath": "EmployeeRatingSystem.Blazor#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "c0plplmksr", "Integrity": "3P5fhYupIysEQAmLCbC5XMIlCostatCcNS53sJrS5q4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\bundle\\EmployeeRatingSystem.Blazor.styles.css", "FileLength": 6070, "LastWriteTime": "2025-07-20T05:21:37+00:00"}, {"Identity": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\EmployeeRatingSystem.Blazor.bundle.scp.css", "SourceId": "EmployeeRatingSystem.Blazor", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "RelativePath": "EmployeeRatingSystem.Blazor#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "qjjuhwdnct", "Integrity": "dn+J8GpIbr87Vc+dSz1r/QsOgs2QST9tcvir6l0t14M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\EmployeeRatingSystem.Blazor.bundle.scp.css", "FileLength": 5937, "LastWriteTime": "2025-07-20T05:21:37+00:00"}, {"Identity": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\css\\app.css", "SourceId": "EmployeeRatingSystem.Blazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b9v9nr18p3", "Integrity": "gSbmxaEWbGnIkz7ucblXFBBCmBc21eNRFindYgmNOG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 14697, "LastWriteTime": "2025-07-20T07:45:42+00:00"}], "Endpoints": [{"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 16:22:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "25o87uqmvr"}, {"Name": "integrity", "Value": "sha256-Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0="}, {"Name": "label", "Value": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.bundle.scp.css"}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 16:22:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0="}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.eu7w3d5g1a.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\QuickGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 14:22:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eu7w3d5g1a"}, {"Name": "integrity", "Value": "sha256-tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU="}, {"Name": "label", "Value": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.razor.js"}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\QuickGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 14:22:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "css/app.b9v9nr18p3.css", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14697"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gSbmxaEWbGnIkz7ucblXFBBCmBc21eNRFindYgmNOG8=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 07:45:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b9v9nr18p3"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-gSbmxaEWbGnIkz7ucblXFBBCmBc21eNRFindYgmNOG8="}]}, {"Route": "css/app.css", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14697"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gSbmxaEWbGnIkz7ucblXFBBCmBc21eNRFindYgmNOG8=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 07:45:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gSbmxaEWbGnIkz7ucblXFBBCmBc21eNRFindYgmNOG8="}]}, {"Route": "EmployeeRatingSystem.Blazor.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\EmployeeRatingSystem.Blazor.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5937"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dn+J8GpIbr87Vc+dSz1r/QsOgs2QST9tcvir6l0t14M=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 05:21:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dn+J8GpIbr87Vc+dSz1r/QsOgs2QST9tcvir6l0t14M="}]}, {"Route": "EmployeeRatingSystem.Blazor.c0plplmksr.styles.css", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\bundle\\EmployeeRatingSystem.Blazor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6070"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3P5fhYupIysEQAmLCbC5XMIlCostatCcNS53sJrS5q4=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 05:21:37 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c0plplmksr"}, {"Name": "integrity", "Value": "sha256-3P5fhYupIysEQAmLCbC5XMIlCostatCcNS53sJrS5q4="}, {"Name": "label", "Value": "EmployeeRatingSystem.Blazor.styles.css"}]}, {"Route": "EmployeeRatingSystem.Blazor.qjjuhwdnct.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\EmployeeRatingSystem.Blazor.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5937"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dn+J8GpIbr87Vc+dSz1r/QsOgs2QST9tcvir6l0t14M=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 05:21:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qjjuhwdnct"}, {"Name": "label", "Value": "EmployeeRatingSystem.Blazor.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-dn+J8GpIbr87Vc+dSz1r/QsOgs2QST9tcvir6l0t14M="}]}, {"Route": "EmployeeRatingSystem.Blazor.styles.css", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\bundle\\EmployeeRatingSystem.Blazor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6070"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3P5fhYupIysEQAmLCbC5XMIlCostatCcNS53sJrS5q4=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 05:21:37 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3P5fhYupIysEQAmLCbC5XMIlCostatCcNS53sJrS5q4="}]}]}