{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "EmployeeRatingSystem.Blazor.c0plplmksr.styles.css", "AssetFile": "EmployeeRatingSystem.Blazor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6070"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3P5fhYupIysEQAmLCbC5XMIlCostatCcNS53sJrS5q4=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 05:21:37 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c0plplmksr"}, {"Name": "integrity", "Value": "sha256-3P5fhYupIysEQAmLCbC5XMIlCostatCcNS53sJrS5q4="}, {"Name": "label", "Value": "EmployeeRatingSystem.Blazor.styles.css"}]}, {"Route": "EmployeeRatingSystem.Blazor.styles.css", "AssetFile": "EmployeeRatingSystem.Blazor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6070"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3P5fhYupIysEQAmLCbC5XMIlCostatCcNS53sJrS5q4=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 05:21:37 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3P5fhYupIysEQAmLCbC5XMIlCostatCcNS53sJrS5q4="}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "AssetFile": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 16:22:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "25o87uqmvr"}, {"Name": "integrity", "Value": "sha256-Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0="}, {"Name": "label", "Value": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.bundle.scp.css"}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.bundle.scp.css", "AssetFile": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 16:22:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0="}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.eu7w3d5g1a.razor.js", "AssetFile": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 14:22:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eu7w3d5g1a"}, {"Name": "integrity", "Value": "sha256-tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU="}, {"Name": "label", "Value": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.razor.js"}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.razor.js", "AssetFile": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 14:22:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "css/app.b9v9nr18p3.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14697"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gSbmxaEWbGnIkz7ucblXFBBCmBc21eNRFindYgmNOG8=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 07:45:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b9v9nr18p3"}, {"Name": "integrity", "Value": "sha256-gSbmxaEWbGnIkz7ucblXFBBCmBc21eNRFindYgmNOG8="}, {"Name": "label", "Value": "css/app.css"}]}, {"Route": "css/app.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14697"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gSbmxaEWbGnIkz7ucblXFBBCmBc21eNRFindYgmNOG8=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 07:45:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gSbmxaEWbGnIkz7ucblXFBBCmBc21eNRFindYgmNOG8="}]}]}