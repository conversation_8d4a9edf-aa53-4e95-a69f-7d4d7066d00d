@page "/dashboard"
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@inherits LocalizedComponentBase

<div class="container-fluid px-4">
    <!-- Enhanced Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-gradient-primary text-white">
                <div class="card-body py-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h1 class="card-title mb-2 fw-bold">
                                <i class="fas fa-tachometer-alt me-3"></i>
                                @L("Dashboard", "لوحة التحكم")
                            </h1>
                            <p class="card-text mb-0 opacity-90">
                                @L("Overview of the Employee Rating System", "نظرة عامة على نظام تقييم الموظفين")
                            </p>
                        </div>
                        <div class="text-end d-none d-md-block">
                            <div class="fs-6 opacity-75">@L("Version 2.0 - Enhanced Enterprise Edition", "الإصدار 2.0 - النسخة المحسنة للمؤسسات")</div>
                            <div class="fs-6 opacity-75">@L("All rights reserved.", "جميع الحقوق محفوظة.")</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="row g-4 mb-5">
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card border-0 shadow-sm h-100 stat-card bg-gradient-primary text-white">
                <div class="card-body text-center p-4">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-building fa-2x opacity-75"></i>
                    </div>
                    <h3 class="fw-bold mb-1">5</h3>
                    <h6 class="mb-0 opacity-90">@L("Departments", "الأقسام")</h6>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card border-0 shadow-sm h-100 stat-card bg-gradient-info text-white">
                <div class="card-body text-center p-4">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                    <h3 class="fw-bold mb-1">42</h3>
                    <h6 class="mb-0 opacity-90">@L("Employees", "الموظفين")</h6>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card border-0 shadow-sm h-100 stat-card bg-gradient-success text-white">
                <div class="card-body text-center p-4">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-clipboard-list fa-2x opacity-75"></i>
                    </div>
                    <h3 class="fw-bold mb-1">128</h3>
                    <h6 class="mb-0 opacity-90">@L("Evaluations", "التقييمات")</h6>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card border-0 shadow-sm h-100 stat-card bg-gradient-warning text-white">
                <div class="card-body text-center p-4">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-star fa-2x opacity-75"></i>
                    </div>
                    <h3 class="fw-bold mb-1">4.2</h3>
                    <h6 class="mb-0 opacity-90">@L("Avg Rating", "المتوسط")</h6>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Main Content Row -->
    <div class="row g-4">
        <!-- Quick Actions -->
        <div class="col-12 col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom-0 py-3">
                    <h5 class="mb-0 fw-bold text-primary">
                        <i class="fas fa-bolt @GetMarginEnd()"></i>
                        @L("Quick Actions", "الإجراءات السريعة")
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        <div class="col-12 col-md-6">
                            <a href="/evaluations/create" class="btn btn-primary w-100 py-3 shadow-sm">
                                <i class="fas fa-plus @GetMarginEnd()"></i>
                                @L("Create New Evaluation", "إنشاء تقييم جديد")
                            </a>
                        </div>
                        <div class="col-12 col-md-6">
                            <a href="/departments" class="btn btn-outline-secondary w-100 py-3 shadow-sm">
                                <i class="fas fa-building @GetMarginEnd()"></i>
                                @L("Manage Departments", "إدارة الأقسام")
                            </a>
                        </div>
                        <div class="col-12 col-md-6">
                            <a href="/users" class="btn btn-outline-info w-100 py-3 shadow-sm">
                                <i class="fas fa-users @GetMarginEnd()"></i>
                                @L("Manage Users", "إدارة المستخدمين")
                            </a>
                        </div>
                        <div class="col-12 col-md-6">
                            <a href="/reports" class="btn btn-outline-success w-100 py-3 shadow-sm">
                                <i class="fas fa-chart-bar @GetMarginEnd()"></i>
                                @L("View Reports", "عرض التقارير")
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced System Information -->
        <div class="col-12 col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom-0 py-3">
                    <h5 class="mb-0 fw-bold text-primary">
                        <i class="fas fa-info-circle @GetMarginEnd()"></i>
                        @L("System Information", "معلومات النظام")
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-2 @GetMarginEnd()">
                            <i class="fas fa-code-branch text-primary"></i>
                        </div>
                        <div>
                            <div class="fw-semibold">@L("Version", "الإصدار")</div>
                            <div class="text-muted small">2.0</div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-success bg-opacity-10 rounded-circle p-2 @GetMarginEnd()">
                            <i class="fas fa-crown text-success"></i>
                        </div>
                        <div>
                            <div class="fw-semibold">@L("Edition", "النسخة")</div>
                            <div class="text-muted small">@L("Enterprise", "المؤسسات")</div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-4">
                        <div class="bg-info bg-opacity-10 rounded-circle p-2 @GetMarginEnd()">
                            <i class="fas fa-globe text-info"></i>
                        </div>
                        <div>
                            <div class="fw-semibold">@L("Language", "اللغة")</div>
                            <div class="text-muted small">English / العربية</div>
                        </div>
                    </div>
                    <hr class="my-3">
                    <div class="text-center">
                        <p class="text-muted mb-0 small">
                            @L("© 2025 Employee Rating System", "© 2025 نظام تقييم الموظفين")<br>
                            @L("All rights reserved.", "جميع الحقوق محفوظة.")
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    // Add any necessary code-behind logic here
}
