/* ===== EMPLOYEE RATING SYSTEM - BLAZOR CSS ===== */

/* ===== BASE RESET AND VARIABLES ===== */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #e9ecef;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    --box-shadow-lg: 0 8px 25px rgba(0,0,0,0.15);
    --transition: all 0.3s ease;
    --navbar-height: 70px;
}

/* ===== GLOBAL LAYOUT ===== */
html {
    font-size: 16px;
    height: 100%;
    overflow-x: hidden;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    color: var(--dark-color);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
}

/* ===== CONTAINER SYSTEM ===== */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
}

.main-content {
    padding-top: calc(var(--navbar-height) + 2rem);
    padding-bottom: 2rem;
    min-height: calc(100vh - var(--navbar-height));
    width: 100%;
    overflow-x: hidden;
}

.content-wrapper {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
}

/* ===== BOOTSTRAP GRID SYSTEM ===== */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

/* ===== ENHANCED DASHBOARD STYLES ===== */
.stat-card {
    transition: var(--transition);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    margin: 0 auto;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #20c997 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #fd7e14 100%);
}

.row > * {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

/* Column classes */
.col {
    flex: 1 0 0%;
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
    .col-sm { flex: 1 0 0%; }
    .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
    .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-sm-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
    .col-sm-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-sm-9 { flex: 0 0 75%; max-width: 75%; }
    .col-sm-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-sm-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
    .col-md { flex: 1 0 0%; }
    .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-md-9 { flex: 0 0 75%; max-width: 75%; }
    .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
    .col-lg { flex: 1 0 0%; }
    .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
    .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-lg-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
    .col-lg-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-lg-9 { flex: 0 0 75%; max-width: 75%; }
    .col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-lg-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
}

/* ===== CARD COMPONENTS ===== */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0,0,0,.125);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.card-body {
    flex: 1 1 auto;
    padding: 1.5rem;
}

.card-title {
    margin-bottom: 0.75rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
}

.card-text {
    margin-bottom: 1rem;
    color: #6c757d;
}

.card-header {
    padding: 1rem 1.5rem;
    margin-bottom: 0;
    background-color: rgba(0,0,0,.03);
    border-bottom: 1px solid rgba(0,0,0,.125);
    font-weight: 600;
    color: var(--dark-color);
}

.card-footer {
    padding: 1rem 1.5rem;
    background-color: rgba(0,0,0,.03);
    border-top: 1px solid rgba(0,0,0,.125);
}

/* ===== BUTTON STYLES ===== */
.btn {
    display: inline-block;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.625rem 1.25rem;
    font-size: 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    color: #fff;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #5a6fd8;
    border-color: #5a6fd8;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #5a6268;
}

.btn-info {
    color: #fff;
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-info:hover {
    background-color: #138496;
    border-color: #138496;
}

.btn-success {
    color: #fff;
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #218838;
    border-color: #218838;
}

/* ===== GRADIENT BACKGROUNDS ===== */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #20c997 100%);
    color: white;
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    color: white;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #fd7e14 100%);
    color: white;
}

/* ===== UTILITY CLASSES ===== */
.d-flex { display: flex !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-none { display: none !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-center { align-items: center !important; }
.align-items-end { align-items: flex-end !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-between { justify-content: space-between !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-end { text-align: right !important; }

.text-white { color: #fff !important; }
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: #6c757d !important; }
.text-success { color: var(--success-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }

.bg-white { background-color: #fff !important; }
.bg-light { background-color: var(--light-color) !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 1rem !important; }
.m-4 { margin: 1.5rem !important; }
.m-5 { margin: 3rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.me-0 { margin-right: 0 !important; }
.me-1 { margin-right: 0.25rem !important; }
.me-2 { margin-right: 0.5rem !important; }
.me-3 { margin-right: 1rem !important; }
.me-4 { margin-right: 1.5rem !important; }
.me-5 { margin-right: 3rem !important; }

.ms-0 { margin-left: 0 !important; }
.ms-1 { margin-left: 0.25rem !important; }
.ms-2 { margin-left: 0.5rem !important; }
.ms-3 { margin-left: 1rem !important; }
.ms-4 { margin-left: 1.5rem !important; }
.ms-5 { margin-left: 3rem !important; }

.gap-0 { gap: 0 !important; }
.gap-1 { gap: 0.25rem !important; }
.gap-2 { gap: 0.5rem !important; }
.gap-3 { gap: 1rem !important; }
.gap-4 { gap: 1.5rem !important; }
.gap-5 { gap: 3rem !important; }

.rounded { border-radius: var(--border-radius) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }

.shadow { box-shadow: var(--box-shadow) !important; }
.shadow-lg { box-shadow: var(--box-shadow-lg) !important; }

.fw-normal { font-weight: 400 !important; }
.fw-bold { font-weight: 700 !important; }
.fw-bolder { font-weight: 900 !important; }

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 575.98px) {
    .d-sm-none { display: none !important; }
    .d-sm-block { display: block !important; }
    .text-sm-center { text-align: center !important; }
}

@media (max-width: 767.98px) {
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
    .text-md-center { text-align: center !important; }
}

@media (max-width: 991.98px) {
    .d-lg-none { display: none !important; }
    .d-lg-block { display: block !important; }
    .text-lg-center { text-align: center !important; }
}

/* ===== NAVIGATION STYLES ===== */
.navbar {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    background-color: #fff;
    border-bottom: 1px solid var(--border-color);
}

.navbar.fixed-top {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
}

.navbar-brand {
    padding-top: 0.3125rem;
    padding-bottom: 0.3125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    text-decoration: none;
    white-space: nowrap;
    color: var(--dark-color);
    font-weight: 600;
}

.navbar-nav {
    display: flex;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

.navbar-nav .nav-link {
    padding-right: 0;
    padding-left: 0;
    color: var(--dark-color);
    text-decoration: none;
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color);
}

.navbar-expand-lg .navbar-nav {
    flex-direction: row;
}

.navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
}

/* ===== FOOTER STYLES ===== */
.footer {
    background-color: var(--dark-color);
    color: #fff;
    padding: 2rem 0;
    margin-top: auto;
}

.footer p {
    margin-bottom: 0;
    font-size: 0.875rem;
}

/* ===== ENHANCED RESPONSIVE LAYOUT ===== */
@media (max-width: 768px) {
    .container, .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }

    .row {
        margin-right: -15px;
        margin-left: -15px;
    }

    .row > * {
        padding-right: 15px;
        padding-left: 15px;
    }

    .main-content {
        padding-top: calc(var(--navbar-height) + 1rem);
        padding-bottom: 1rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .stat-card .card-body {
        padding: 2rem 1rem;
    }

    .btn {
        margin-bottom: 0.5rem;
    }

    /* Improve mobile navigation */
    .navbar-nav {
        padding-top: 1rem;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }
}

@media (max-width: 576px) {
    .container, .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    .row {
        margin-right: -10px;
        margin-left: -10px;
    }

    .row > * {
        padding-right: 10px;
        padding-left: 10px;
    }

    .card-body {
        padding: 1rem;
    }

    .stat-card .card-body {
        padding: 1.5rem 0.75rem;
    }

    .card-title {
        font-size: 1.25rem;
    }

    /* Improve small screen layout */
    .stat-card h3 {
        font-size: 1.75rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
    }

    .stat-icon i {
        font-size: 1.25rem;
    }
}

/* ===== ADDITIONAL LAYOUT IMPROVEMENTS ===== */
.card {
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(0,0,0,0.08);
}

.card-header {
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    background: rgba(0,0,0,0.02);
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

/* Fix any potential z-index issues */
.navbar {
    z-index: 1030;
}

.dropdown-menu {
    z-index: 1040;
}

/* ===== RTL SUPPORT AND BILINGUAL LAYOUT ===== */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .navbar-brand {
    margin-right: 0;
    margin-left: auto;
}

[dir="rtl"] .navbar-nav {
    margin-right: auto;
    margin-left: 0;
}

[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

[dir="rtl"] .text-end {
    text-align: left !important;
}

[dir="rtl"] .text-start {
    text-align: right !important;
}

[dir="rtl"] .me-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

[dir="rtl"] .ms-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

/* RTL spacing utilities */
[dir="rtl"] .me-1 { margin-right: 0 !important; margin-left: 0.25rem !important; }
[dir="rtl"] .me-2 { margin-right: 0 !important; margin-left: 0.5rem !important; }
[dir="rtl"] .me-3 { margin-right: 0 !important; margin-left: 1rem !important; }
[dir="rtl"] .ms-1 { margin-left: 0 !important; margin-right: 0.25rem !important; }
[dir="rtl"] .ms-2 { margin-left: 0 !important; margin-right: 0.5rem !important; }
[dir="rtl"] .ms-3 { margin-left: 0 !important; margin-right: 1rem !important; }

/* RTL card layout */
[dir="rtl"] .card-header,
[dir="rtl"] .card-body,
[dir="rtl"] .card-footer {
    text-align: right;
}

[dir="rtl"] .d-flex {
    flex-direction: row-reverse;
}

[dir="rtl"] .d-flex.flex-column {
    flex-direction: column;
}

/* Ensure icons maintain proper spacing in RTL */
[dir="rtl"] .fas,
[dir="rtl"] .far,
[dir="rtl"] .fab {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* ===== PREVENT HORIZONTAL OVERFLOW ===== */
html, body {
    max-width: 100vw;
    overflow-x: hidden;
}

.page {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
}

* {
    max-width: 100%;
}

/* ===== FADE IN ANIMATION ===== */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
